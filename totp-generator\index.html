<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>TOTP Generator</title>
  <link rel="stylesheet" href="css/bulma-0.9.4.min.css">
  <style>
    [v-cloak] {
      display: none;
    }

    html, body {
      height: 100%;
    }

    body > footer {
      position: sticky;
      top: 100vh;
    }

    .footer {
      padding: 2rem;
    }

    @media screen and (min-width: 1068px) {
      .container {
        max-width: 600px;
        width: 600px;
      }
    }
  </style>
</head>
<body>

  <script defer src="js/vue-3.3.2.global.prod.js"></script>
  <script defer src="js/otpauth-9.1.3.min.js"></script>
  <script defer src="js/clipboard-2.0.6.min.js"></script>
  <script defer src="js/app.js?v=1.2"></script>

  <section id="app" class="section" v-cloak>

    <div class="container">

      <h1 class="title">TOTP Token Generator</h1>

      <div class="field">
        <label class="label is-uppercase">Your Secret Key</label>
        <div class="control">
          <input class="input" type="text" v-model="secret_key" placeholder="The secret key (in base-32 format)">
        </div>
      </div>

      <div class="field">
        <label class="label is-uppercase">Number of Digits</label>
        <div class="control">
          <input class="input" type="text" v-model="digits" placeholder="Usually 6">
        </div>
      </div>

      <div class="field">
        <label class="label is-uppercase">Token Period (in seconds)</label>
        <div class="control">
          <input class="input" type="text" v-model="period" placeholder="Usually 30">
        </div>
      </div>

      <div class="content">
        <span class="has-text-grey is-size-7">Updating in {{ updatingIn }} seconds</span>
        <progress class="progress is-info is-small" v-bind:value="updatingIn" :max="period"></progress>
      </div>

      <div class="box">
        <p class="title is-size-1 has-text-centered" id="token">{{ token }}</p>
      </div>

      <div class="control is-clearfix">
        <a class="button is-large is-light is-pulled-right" id="clipboard-button" data-clipboard-target="#token" title="Copy token to clipboard">
          <img src="img/clippy.svg" height="36" width="36">
        </a>
      </div>

    </div>

  </section>

  <footer class="footer">
    <div class="container">
      <div class="content has-text-centered">
        Built by <a href="https://dan.hersam.com">Dan Hersam</a>. Source on <a href="https://github.com/jaden/totp-generator">Github</a>.
      </div>
    </div>
  </footer>

</body>
</html>

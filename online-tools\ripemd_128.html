<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="content-language" content="en"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>RIPEMD-128 - Online Tools</title><meta name="keywords" content="RIPEMD-128, online, hash, HMAC, UTF-8, UTF-16, Hex"><meta name="author" content="emn178"><meta name="copyright" content="emn178"><meta name="description" content="This RIPEMD-128 online tool helps you calculate hash from string or binary. You can input UTF-8, UTF-16, Hex to RIPEMD-128. It also supports HMAC."><meta property="og:type" content="website"><meta property="og:title" content="RIPEMD-128"><meta property="og:url" content="https://emn178.github.io/online-tools/ripemd_128.html"><meta property="og:site_name" content="Online Tools"><meta property="og:description" content="This RIPEMD-128 online tool helps you calculate hash from string or binary. You can input UTF-8, UTF-16, Hex to RIPEMD-128. It also supports HMAC."><meta property="og:locale" content="en"><meta property="og:image" content="https://emn178.github.io/online-tools/images/logo.png"><meta property="article:author" content="emn178"><meta property="article:tag" content="RIPEMD-128, online, hash, HMAC, UTF-8, UTF-16, Hex"><meta property="twitter:card" content="summary"><meta property="twitter:image" content="https://emn178.github.io/online-tools/images/logo.png"><link rel="canonical" href="https://emn178.github.io/online-tools/ripemd_128.html"><link rel="icon" type="image/x-icon" href="favicon.ico"><script>if (location.hostname != 'localhost') {
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WT6N5R6W6Z');
}

var delayScripts = [];
var waitLoadCount = 0;
(function () {
  var gaUrl = 'https://www.googletagmanager.com/gtag/js?id=G-WT6N5R6W6Z';

  function initGTMOnEvent(e) {
    document.removeEventListener(event.type, initGTMOnEvent);
    initGTM();
  }

  function initGTM() {
    if (window.gtmDidInit) {
      return;
    }
    window.gtmDidInit = true;
    var script = document.createElement('script');
    script.async = true;
    script.src = gaUrl;
    document.head.appendChild(script);
  }

  if (document.addEventListener) {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(initGTM, 3500);
    });
    document.addEventListener('scroll', initGTMOnEvent);
    document.addEventListener('mousemove', initGTMOnEvent);
    document.addEventListener('touchstart', initGTMOnEvent);
  } else {
    delayScripts.push({
      src: gaUrl
    });
  }
})();
</script><link rel="stylesheet" href="css/style.css?v=10"></head><body><header class="navbar"><div class="container"><div class="site-name"><a href="./">Online Tools</a></div><div class="navbar-toggler">Menu</div><div class="navbar-nav flex-1"><div class="nav-item"><div class="nav-dropdown">Hash</div><div class="nav-dropdown-content"><div class="container"><ul class="menu"><li><div class="menu-group">CRC</div></li><li><a href="crc16.html">CRC-16</a></li><li><a href="crc16_checksum.html">CRC-16 File</a></li><li><a href="crc32.html">CRC-32</a></li><li><a href="crc32_checksum.html">CRC-32 File</a></li></ul><ul class="menu"><li><div class="menu-group">MD</div></li><li><a href="md2.html">MD2</a></li><li><a href="md2_file_hash.html">MD2 File</a></li><li><a href="md4.html">MD4</a></li><li><a href="md4_file_hash.html">MD4 File</a></li><li><a href="md5.html">MD5</a></li><li><a href="md5_checksum.html">MD5 File</a></li></ul><ul class="menu"><li><div class="menu-group">SHA1</div></li><li><a href="sha1.html">SHA1</a></li><li><a href="sha1_checksum.html">SHA1 File</a></li></ul><ul class="menu"><li><div class="menu-group">SHA2</div></li><li><a href="sha224.html">SHA224</a></li><li><a href="sha224_checksum.html">SHA224 File</a></li><li><a href="sha256.html">SHA256</a></li><li><a href="sha256_checksum.html">SHA256 File</a></li><li><a href="double_sha256.html">Double SHA256</a></li></ul><ul class="menu"><li><div class="menu-group">SHA2-512</div></li><li><a href="sha384.html">SHA384</a></li><li><a href="sha384_file_hash.html">SHA384 File</a></li><li><a href="sha512.html">SHA512</a></li><li><a href="sha512_file_hash.html">SHA512 File</a></li><li><a href="sha512_224.html">SHA512/224</a></li><li><a href="sha512_224_file_hash.html">SHA512/224 File</a></li><li><a href="sha512_256.html">SHA512/256</a></li><li><a href="sha512_256_file_hash.html">SHA512/256 File</a></li></ul><ul class="menu"><li><div class="menu-group">SHA3</div></li><li><a href="sha3_224.html">SHA3-224</a></li><li><a href="sha3_224_checksum.html">SHA3-224 File</a></li><li><a href="sha3_256.html">SHA3-256</a></li><li><a href="sha3_256_checksum.html">SHA3-256 File</a></li><li><a href="sha3_384.html">SHA3-384</a></li><li><a href="sha3_384_checksum.html">SHA3-384 File</a></li><li><a href="sha3_512.html">SHA3-512</a></li><li><a href="sha3_512_checksum.html">SHA3-512 File</a></li></ul><ul class="menu"><li><div class="menu-group">Keccak</div></li><li><a href="keccak_224.html">Keccak-224</a></li><li><a href="keccak_224_checksum.html">Keccak-224 File</a></li><li><a href="keccak_256.html">Keccak-256</a></li><li><a href="keccak_256_checksum.html">Keccak-256 File</a></li><li><a href="keccak_384.html">Keccak-384</a></li><li><a href="keccak_384_checksum.html">Keccak-384 File</a></li><li><a href="keccak_512.html">Keccak-512</a></li><li><a href="keccak_512_checksum.html">Keccak-512 File</a></li></ul><ul class="menu"><li><div class="menu-group">SHAKE</div></li><li><a href="shake_128.html">SHAKE128</a></li><li><a href="shake_128_checksum.html">SHAKE128 File</a></li><li><a href="shake_256.html">SHAKE256</a></li><li><a href="shake_256_checksum.html">SHAKE256 File</a></li></ul><ul class="menu"><li><div class="menu-group">cSHAKE</div></li><li><a href="cshake128.html">cSHAKE128</a></li><li><a href="cshake128_file_hash.html">cSHAKE128 File</a></li><li><a href="cshake256.html">cSHAKE256</a></li><li><a href="cshake256_file_hash.html">cSHAKE256 File</a></li></ul><ul class="menu"><li><div class="menu-group">KMAC</div></li><li><a href="kmac128.html">KMAC128</a></li><li><a href="kmac128_file_hash.html">KMAC128 File</a></li><li><a href="kmac256.html">KMAC256</a></li><li><a href="kmac256_file_hash.html">KMAC256 File</a></li></ul><ul class="menu"><li><div class="menu-group">RIPEMD</div></li><li><a href="ripemd_128.html">RIPEMD-128</a></li><li><a href="ripemd_128_checksum.html">RIPEMD-128 File</a></li><li><a href="ripemd_160.html">RIPEMD-160</a></li><li><a href="ripemd_160_checksum.html">RIPEMD-160 File</a></li><li><a href="ripemd_256.html">RIPEMD-256</a></li><li><a href="ripemd_256_checksum.html">RIPEMD-256 File</a></li><li><a href="ripemd_320.html">RIPEMD-320</a></li><li><a href="ripemd_320_checksum.html">RIPEMD-320 File</a></li></ul></div></div></div><div class="nav-item"><div class="nav-dropdown">Encoding</div><div class="nav-dropdown-content"><div class="container"><ul class="menu"><li><div class="menu-group">Base32</div></li><li><a href="base32_encode.html">Encode</a></li><li><a href="base32_decode.html">Decode</a></li><li><a href="base32_encode_file.html">File to Base32</a></li><li><a href="base32_decode_file.html">Base32 to File</a></li></ul><ul class="menu"><li><div class="menu-group">Base58</div></li><li><a href="base58_encode.html">Encode</a></li><li><a href="base58_decode.html">Decode</a></li><li><a href="base58_encode_file.html">File to Base58</a></li><li><a href="base58_decode_file.html">Base58 to File</a></li></ul><ul class="menu"><li><div class="menu-group">Base64</div></li><li><a href="base64_encode.html">Encode</a></li><li><a href="base64_decode.html">Decode</a></li><li><a href="base64_encode_file.html">File to Base64</a></li><li><a href="base64_decode_file.html">Base64 to File</a></li></ul><ul class="menu"><li><div class="menu-group">HTML</div></li><li><a href="html_encode.html">Encode</a></li><li><a href="html_decode.html">Decode</a></li></ul><ul class="menu"><li><div class="menu-group">URL</div></li><li><a href="url_encode.html">Encode</a></li><li><a href="url_decode.html">Decode</a></li></ul></div></div></div><div class="nav-item"><div class="nav-dropdown">Misc</div><div class="nav-dropdown-content"><div class="container"><ul class="menu"><li><div class="menu-group">Misc</div></li><li><a href="syntax_highlight.html">Syntax Highlight</a></li></ul></div></div></div><div class="nav-item ms-auto"><a class="nav-link" href="https://github.com/emn178/online-tools/issues">Contact</a></div></div></div></header><div class="container body px-2"><div class="flex-1"><h1>RIPEMD-128</h1><div class="description">This RIPEMD-128 online tool helps you calculate hash from string or binary. You can input UTF-8, UTF-16, Hex to RIPEMD-128. It also supports HMAC.</div><div class="input"><div class="option-block"><label for="input-type">Input Type</label><select class="ms-2" id="input-type" data-option="true" data-remember="input-type" data-share="input_type"><optgroup label="Binary"><option value="hex">Hex</option></optgroup><optgroup label="Text"><option value="utf-8" selected="selected">UTF-8</option><option value="utf-16le" data-load-encoding="1">UTF-16LE</option><option value="utf-16be" data-load-encoding="1">UTF-16BE</option><option value="ibm866" data-load-encoding="2">IBM866</option><option value="iso-8859-2" data-load-encoding="2">ISO-8859-2</option><option value="iso-8859-3" data-load-encoding="2">ISO-8859-3</option><option value="iso-8859-4" data-load-encoding="2">ISO-8859-4</option><option value="iso-8859-5" data-load-encoding="2">ISO-8859-5</option><option value="iso-8859-6" data-load-encoding="2">ISO-8859-6</option><option value="iso-8859-7" data-load-encoding="2">ISO-8859-7</option><option value="iso-8859-8" data-load-encoding="2">ISO-8859-8</option><option value="iso-8859-8-i" data-load-encoding="2">ISO-8859-8-I</option><option value="iso-8859-10" data-load-encoding="2">ISO-8859-10</option><option value="iso-8859-13" data-load-encoding="2">ISO-8859-13</option><option value="iso-8859-14" data-load-encoding="2">ISO-8859-14</option><option value="iso-8859-15" data-load-encoding="2">ISO-8859-15</option><option value="iso-8859-16" data-load-encoding="2">ISO-8859-16</option><option value="koi8-r" data-load-encoding="2">KOI8-R</option><option value="koi8-u" data-load-encoding="2">KOI8-U</option><option value="macintosh" data-load-encoding="2">macintosh</option><option value="windows-874" data-load-encoding="2">Windows-874</option><option value="windows-1250" data-load-encoding="2">Windows-1250</option><option value="windows-1251" data-load-encoding="2">Windows-1251</option><option value="windows-1252" data-load-encoding="2">Windows-1252</option><option value="windows-1253" data-load-encoding="2">Windows-1253</option><option value="windows-1254" data-load-encoding="2">Windows-1254</option><option value="windows-1255" data-load-encoding="2">Windows-1255</option><option value="windows-1256" data-load-encoding="2">Windows-1256</option><option value="windows-1257" data-load-encoding="2">Windows-1257</option><option value="windows-1258" data-load-encoding="2">Windows-1258</option><option value="x-mac-cyrillic" data-load-encoding="2">x-mac-cyrillic</option><option value="gbk" data-load-encoding="2">GBK</option><option value="gb18030" data-load-encoding="2">gb18030</option><option value="big5" data-load-encoding="2">Big5</option><option value="euc-jp" data-load-encoding="2">EUC-JP</option><option value="iso-2022-jp" data-load-encoding="2">ISO-2022-JP</option><option value="shift_jis" data-load-encoding="2">Shift_JIS</option><option value="euc-kr" data-load-encoding="2">EUC-KR</option><option value="x-user-defined" data-load-encoding="2">x-user-defined</option></optgroup></select></div><textarea id="input" placeholder="Input" data-remember="input" data-share="input"></textarea><div class="option-block"><label><input id="remember-input" type="checkbox" value="1">Remember Input</label></div><div class="hmac"><div class="option-block"><label><input id="hmac-enabled" type="checkbox" value="1" data-remember="hmac-enabled" data-share="hmac_enabled">Enable HMAC</label></div><div id="hmac" style="display:none"><div class="d-flex align-items-center input-type-group"><div class="title">HMAC</div><div class="option-block"><label for="hmac-input-type">Input Type</label><select class="ms-2" id="hmac-input-type" data-option="true" data-remember="hmac-input-type" data-share="hmac_input_type"><optgroup label="Binary"><option value="hex">Hex</option></optgroup><optgroup label="Text"><option value="utf-8" selected="selected">UTF-8</option><option value="utf-16le" data-load-encoding="1">UTF-16LE</option><option value="utf-16be" data-load-encoding="1">UTF-16BE</option><option value="ibm866" data-load-encoding="2">IBM866</option><option value="iso-8859-2" data-load-encoding="2">ISO-8859-2</option><option value="iso-8859-3" data-load-encoding="2">ISO-8859-3</option><option value="iso-8859-4" data-load-encoding="2">ISO-8859-4</option><option value="iso-8859-5" data-load-encoding="2">ISO-8859-5</option><option value="iso-8859-6" data-load-encoding="2">ISO-8859-6</option><option value="iso-8859-7" data-load-encoding="2">ISO-8859-7</option><option value="iso-8859-8" data-load-encoding="2">ISO-8859-8</option><option value="iso-8859-8-i" data-load-encoding="2">ISO-8859-8-I</option><option value="iso-8859-10" data-load-encoding="2">ISO-8859-10</option><option value="iso-8859-13" data-load-encoding="2">ISO-8859-13</option><option value="iso-8859-14" data-load-encoding="2">ISO-8859-14</option><option value="iso-8859-15" data-load-encoding="2">ISO-8859-15</option><option value="iso-8859-16" data-load-encoding="2">ISO-8859-16</option><option value="koi8-r" data-load-encoding="2">KOI8-R</option><option value="koi8-u" data-load-encoding="2">KOI8-U</option><option value="macintosh" data-load-encoding="2">macintosh</option><option value="windows-874" data-load-encoding="2">Windows-874</option><option value="windows-1250" data-load-encoding="2">Windows-1250</option><option value="windows-1251" data-load-encoding="2">Windows-1251</option><option value="windows-1252" data-load-encoding="2">Windows-1252</option><option value="windows-1253" data-load-encoding="2">Windows-1253</option><option value="windows-1254" data-load-encoding="2">Windows-1254</option><option value="windows-1255" data-load-encoding="2">Windows-1255</option><option value="windows-1256" data-load-encoding="2">Windows-1256</option><option value="windows-1257" data-load-encoding="2">Windows-1257</option><option value="windows-1258" data-load-encoding="2">Windows-1258</option><option value="x-mac-cyrillic" data-load-encoding="2">x-mac-cyrillic</option><option value="gbk" data-load-encoding="2">GBK</option><option value="gb18030" data-load-encoding="2">gb18030</option><option value="big5" data-load-encoding="2">Big5</option><option value="euc-jp" data-load-encoding="2">EUC-JP</option><option value="iso-2022-jp" data-load-encoding="2">ISO-2022-JP</option><option value="shift_jis" data-load-encoding="2">Shift_JIS</option><option value="euc-kr" data-load-encoding="2">EUC-KR</option><option value="x-user-defined" data-load-encoding="2">x-user-defined</option></optgroup></select></div><div class="option-block flex-1"><input id="hmac-key" type="text" data-remember="hmac-key" data-share="hmac_key"></div></div></div></div></div><div class="submit"><button class="btn btn-default" id="execute">Hash</button><label><input id="auto-update" type="checkbox" value="1" checked="checked">Auto Update</label></div><div class="output"><textarea id="output" placeholder="Output" readonly></textarea></div><div class="submit"><button class="copy btn btn-default" data-clipboard-target="#output">Copy</button></div><div class="input-group"><input id="share-link" type="text" placeholder="Share Link" readonly><button class="copy btn btn-default" data-clipboard-target="#share-link">Copy</button></div></div><div id="sidebar"><ul class="menu"><li><div class="menu-group">RIPEMD</div></li><li><a href="ripemd_128.html">RIPEMD-128</a></li><li><a href="ripemd_128_checksum.html">RIPEMD-128 File</a></li><li><a href="ripemd_160.html">RIPEMD-160</a></li><li><a href="ripemd_160_checksum.html">RIPEMD-160 File</a></li><li><a href="ripemd_256.html">RIPEMD-256</a></li><li><a href="ripemd_256_checksum.html">RIPEMD-256 File</a></li><li><a href="ripemd_320.html">RIPEMD-320</a></li><li><a href="ripemd_320_checksum.html">RIPEMD-320 File</a></li></ul></div></div><script>++waitLoadCount;
delayScripts.push({
  src: 'js/crypto-api.js',
  onload: function () {
    window.method = hmacable(ripemd128);
    methodLoad();
  }
});</script><script>++waitLoadCount;
delayScripts.push({
  src: 'https://nf404.github.io/crypto-api/crypto-api.min.js',
  onload: function () {
    methodLoad();
  }
});</script><script>delayScripts.push({
  src: 'https://cdn.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js',
  onload: function () {
    new ClipboardJS('.copy');
  },
  delay: 100
});</script><footer class="footer"><div class="copyright">&copy; 2015-2023 Online Tools</div></footer><script src="https://code.jquery.com/jquery-1.10.1.min.js" defer></script><script src="js/main.js?v=12" defer></script></body></html>
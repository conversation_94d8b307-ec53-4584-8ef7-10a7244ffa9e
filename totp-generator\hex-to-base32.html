<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Convert hexadecimal to base-32</title>
  <link rel="stylesheet" href="css/bulma-0.7.1.min.css">
  <style>
    @media screen and (min-width: 1068px) {
      .container {
        max-width: 600px;
        width: 600px;
      }
    }
  </style>
</head>
<body>

  <section id="app" class="section">

    <div class="container">

      <h1 class="title">Hex to base-32 Converter</h1>

      <label class="label is-uppercase">Hex string</label>
      <div class="field">
        <div class="control is-expanded">
          <input class="input" type="text" v-model="hex" autofocus placeholder="The secret key (in hex)">
        </div>
      </div>

      <div v-show="base32" class="box" style="background-color: #fafafa">
        <label class="label is-uppercase">Base-32</label>
        <p class="has-text-info is-size-4 has-text-weight-bold">{{ base32 }}</p>
      </div>

    </div>

  </section>

  <footer class="footer">
    <div class="container">
      <div class="content has-text-centered">
        <p>
          Built by <a href="https://dan.hersam.com">Dan Hersam</a>. Source on <a href="https://github.com/jaden/totp-generator">Github</a>.
        </p>
        <p>
          <a class="icon" href="https://github.com/jgthms/bulma">
            <i class="fa fa-github"></i>
          </a>
        </p>
      </div>
    </div>
  </footer>

  <script src="js/vue-2.4.0.min.js"></script>
  <script src="js/otpauth-3.1.3.min.js"></script>
  <script src="js/hex-to-base32.js"></script>

</body>
</html>
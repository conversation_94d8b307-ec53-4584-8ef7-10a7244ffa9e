!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).TextEncoding={})}(this,(function(t){"use strict";var e="utf-8";function n(t,e){if(void 0===e&&(e=void 0),t)throw TypeError("Decoder error");return e||65533}function i(t){throw TypeError("The code point "+t+" could not be encoded.")}function r(t){var e=String(t).trim().toLowerCase();return e in o?o[e]:null}var s=[{encodings:[{labels:["unicode-1-1-utf-8","utf-8","utf8"],name:"UTF-8"}],heading:"The Encoding"},{encodings:[{labels:["866","cp866","csibm866","ibm866"],name:"IBM866"},{labels:["csisolatin2","iso-8859-2","iso-ir-101","iso8859-2","iso88592","iso_8859-2","iso_8859-2:1987","l2","latin2"],name:"ISO-8859-2"},{labels:["csisolatin3","iso-8859-3","iso-ir-109","iso8859-3","iso88593","iso_8859-3","iso_8859-3:1988","l3","latin3"],name:"ISO-8859-3"},{labels:["csisolatin4","iso-8859-4","iso-ir-110","iso8859-4","iso88594","iso_8859-4","iso_8859-4:1988","l4","latin4"],name:"ISO-8859-4"},{labels:["csisolatincyrillic","cyrillic","iso-8859-5","iso-ir-144","iso8859-5","iso88595","iso_8859-5","iso_8859-5:1988"],name:"ISO-8859-5"},{labels:["arabic","asmo-708","csiso88596e","csiso88596i","csisolatinarabic","ecma-114","iso-8859-6","iso-8859-6-e","iso-8859-6-i","iso-ir-127","iso8859-6","iso88596","iso_8859-6","iso_8859-6:1987"],name:"ISO-8859-6"},{labels:["csisolatingreek","ecma-118","elot_928","greek","greek8","iso-8859-7","iso-ir-126","iso8859-7","iso88597","iso_8859-7","iso_8859-7:1987","sun_eu_greek"],name:"ISO-8859-7"},{labels:["csiso88598e","csisolatinhebrew","hebrew","iso-8859-8","iso-8859-8-e","iso-ir-138","iso8859-8","iso88598","iso_8859-8","iso_8859-8:1988","visual"],name:"ISO-8859-8"},{labels:["csiso88598i","iso-8859-8-i","logical"],name:"ISO-8859-8-I"},{labels:["csisolatin6","iso-8859-10","iso-ir-157","iso8859-10","iso885910","l6","latin6"],name:"ISO-8859-10"},{labels:["iso-8859-13","iso8859-13","iso885913"],name:"ISO-8859-13"},{labels:["iso-8859-14","iso8859-14","iso885914"],name:"ISO-8859-14"},{labels:["csisolatin9","iso-8859-15","iso8859-15","iso885915","iso_8859-15","l9"],name:"ISO-8859-15"},{labels:["iso-8859-16"],name:"ISO-8859-16"},{labels:["cskoi8r","koi","koi8","koi8-r","koi8_r"],name:"KOI8-R"},{labels:["koi8-ru","koi8-u"],name:"KOI8-U"},{labels:["csmacintosh","mac","macintosh","x-mac-roman"],name:"macintosh"},{labels:["dos-874","iso-8859-11","iso8859-11","iso885911","tis-620","windows-874"],name:"windows-874"},{labels:["cp1250","windows-1250","x-cp1250"],name:"windows-1250"},{labels:["cp1251","windows-1251","x-cp1251"],name:"windows-1251"},{labels:["ansi_x3.4-1968","cp1252","cp819","ibm819","iso-ir-100","windows-1252","x-cp1252"],name:"windows-1252"},{labels:["ascii","us-ascii","iso-8859-1","iso8859-1","iso88591","iso_8859-1","iso_8859-1:1987","l1","latin1","csisolatin1"],name:"iso-8859-1"},{labels:["cp1253","windows-1253","x-cp1253"],name:"windows-1253"},{labels:["cp1254","csisolatin5","iso-8859-9","iso-ir-148","iso8859-9","iso88599","iso_8859-9","iso_8859-9:1989","l5","latin5","windows-1254","x-cp1254"],name:"windows-1254"},{labels:["cp1255","windows-1255","x-cp1255"],name:"windows-1255"},{labels:["cp1256","windows-1256","x-cp1256"],name:"windows-1256"},{labels:["cp1257","windows-1257","x-cp1257"],name:"windows-1257"},{labels:["cp1258","windows-1258","x-cp1258"],name:"windows-1258"},{labels:["x-mac-cyrillic","x-mac-ukrainian"],name:"x-mac-cyrillic"}],heading:"Legacy single-byte encodings"},{encodings:[{labels:["chinese","csgb2312","csiso58gb231280","gb2312","gb_2312","gb_2312-80","gbk","iso-ir-58","x-gbk"],name:"GBK"},{labels:["gb18030"],name:"gb18030"}],heading:"Legacy multi-byte Chinese (simplified) encodings"},{encodings:[{labels:["big5","big5-hkscs","cn-big5","csbig5","x-x-big5"],name:"Big5"}],heading:"Legacy multi-byte Chinese (traditional) encodings"},{encodings:[{labels:["cseucpkdfmtjapanese","euc-jp","x-euc-jp"],name:"EUC-JP"},{labels:["csiso2022jp","iso-2022-jp"],name:"ISO-2022-JP"},{labels:["csshiftjis","ms932","ms_kanji","shift-jis","shift_jis","sjis","windows-31j","x-sjis"],name:"Shift_JIS"}],heading:"Legacy multi-byte Japanese encodings"},{encodings:[{labels:["cseuckr","csksc56011987","euc-kr","iso-ir-149","korean","ks_c_5601-1987","ks_c_5601-1989","ksc5601","ksc_5601","windows-949"],name:"EUC-KR"}],heading:"Legacy multi-byte Korean encodings"},{encodings:[{labels:["csiso2022kr","hz-gb-2312","iso-2022-cn","iso-2022-cn-ext","iso-2022-kr"],name:"replacement"},{labels:["utf-16be"],name:"UTF-16BE"},{labels:["utf-16","utf-16le"],name:"UTF-16LE"},{labels:["x-user-defined"],name:"x-user-defined"}],heading:"Legacy miscellaneous encodings"}],o={};s.forEach((function(t){t.encodings.forEach((function(t){t.labels.forEach((function(e){o[e]=t}))}))}));var a,u,l,f=-1;function h(t){return Array.isArray(t)?t:[t]}function c(t,e,n){return e<=t&&t<=n}function d(t){if(null==t)return{};if(t===Object(t))return t;throw TypeError("Could not convert argument to dictionary")}function _(){return"undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0}function p(){if(a)return a;var t=function(){if("undefined"!=typeof TextEncodingIndexes)return TextEncodingIndexes.encodingIndexes;var t=_();return t?"TextEncodingIndexes"in t?global.TextEncodingIndexes.encodingIndexes:"encoding-indexes"in t?global.encodingIndexes:null:null}();return t?(a=t,t):null}function g(t,e){return e&&e[t]||null}function b(t,e){var n=e.indexOf(t);return-1===n?null:n}function y(t){var e=p();if(!e)throw Error("Indexes missing. Did you forget to include encoding-indexes.js first?");return e[t]}function w(t){return 0<=t&&t<=127}var m,j=w,v=-1,S=function(){function t(t){this.fatal=t.fatal,this.Big5_lead=0}return t.prototype.handler=function(t,e){if(e===v&&0!==this.Big5_lead)return this.Big5_lead=0,n(this.fatal);if(e===v&&0===this.Big5_lead)return f;if(0!==this.Big5_lead){var i=this.Big5_lead,r=null;this.Big5_lead=0;var s=e<127?64:98;switch((c(e,64,126)||c(e,161,254))&&(r=157*(i-129)+(e-s)),r){case 1133:return[202,772];case 1135:return[202,780];case 1164:return[234,772];case 1166:return[234,780]}var o=null===r?null:g(r,y("big5"));return null===o&&w(e)&&t.prepend(e),null===o?n(this.fatal):o}return w(e)?e:c(e,129,254)?(this.Big5_lead=e,null):n(this.fatal)},t}(),I=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;var n=function(t){var e=l=l||y("big5").map((function(t,e){return e<5024?null:t}));return 9552===t||9566===t||9569===t||9578===t||21313===t||21317===t?e.lastIndexOf(t):b(t,e)}(e);if(null===n)return i(e);var r=Math.floor(n/157)+129;if(r<161)return i(e);var s=n%157;return[r,s+(s<63?64:98)]},t}(),k=function(){function t(t){this.fatal=t.fatal,this.eucjp_jis0212_flag=!1,this.eucjp_lead=0}return t.prototype.handler=function(t,e){if(e===v&&0!==this.eucjp_lead)return this.eucjp_lead=0,n(this.fatal);if(e===v&&0===this.eucjp_lead)return f;if(142===this.eucjp_lead&&c(e,161,223))return this.eucjp_lead=0,65216+e;if(143===this.eucjp_lead&&c(e,161,254))return this.eucjp_jis0212_flag=!0,this.eucjp_lead=e,null;if(0!==this.eucjp_lead){var i=this.eucjp_lead;this.eucjp_lead=0;var r=null;return c(i,161,254)&&c(e,161,254)&&(r=g(94*(i-161)+(e-161),y(this.eucjp_jis0212_flag?"jis0212":"jis0208"))),this.eucjp_jis0212_flag=!1,c(e,161,254)||t.prepend(e),null===r?n(this.fatal):r}return w(e)?e:142===e||143===e||c(e,161,254)?(this.eucjp_lead=e,null):n(this.fatal)},t}(),x=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;if(165===e)return 92;if(8254===e)return 126;if(c(e,65377,65439))return[142,e-65377+161];8722===e&&(e=65293);var n=b(e,y("jis0208"));return null===n?i(e):[Math.floor(n/94)+161,n%94+161]},t}(),E=function(){function t(t){this.fatal=t.fatal,this.euckr_lead=0}return t.prototype.handler=function(t,e){if(e===v&&0!==this.euckr_lead)return this.euckr_lead=0,n(this.fatal);if(e===v&&0===this.euckr_lead)return f;if(0!==this.euckr_lead){var i=this.euckr_lead,r=null;this.euckr_lead=0,c(e,65,254)&&(r=190*(i-129)+(e-65));var s=null===r?null:g(r,y("euc-kr"));return null===r&&w(e)&&t.prepend(e),null===s?n(this.fatal):s}return w(e)?e:c(e,129,254)?(this.euckr_lead=e,null):n(this.fatal)},t}(),B=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;var n=b(e,y("euc-kr"));return null===n?i(e):[Math.floor(n/190)+129,n%190+65]},t}(),O=function(){function t(t){this.fatal=t.fatal,this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0}return t.prototype.handler=function(t,e){if(e===v&&0===this.gb18030_first&&0===this.gb18030_second&&0===this.gb18030_third)return f;var i;if(e!==v||0===this.gb18030_first&&0===this.gb18030_second&&0===this.gb18030_third||(this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0,n(this.fatal)),0!==this.gb18030_third){i=null,c(e,48,57)&&(i=function(t){if(t>39419&&t<189e3||t>1237575)return null;if(7457===t)return 59335;for(var e=0,n=0,i=y("gb18030-ranges"),r=0;r<i.length;++r){var s=h(i[r]);if(!(s[0]<=t))break;e=s[0],n=s[1]}return n+t-e}(10*(126*(10*(this.gb18030_first-129)+this.gb18030_second-48)+this.gb18030_third-129)+e-48));var r=[this.gb18030_second,this.gb18030_third,e];return this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0,null===i?(t.prepend(r),n(this.fatal)):i}if(0!==this.gb18030_second)return c(e,129,254)?(this.gb18030_third=e,null):(t.prepend([this.gb18030_second,e]),this.gb18030_first=0,this.gb18030_second=0,n(this.fatal));if(0!==this.gb18030_first){if(c(e,48,57))return this.gb18030_second=e,null;var s=this.gb18030_first,o=null;this.gb18030_first=0;var a=e<127?64:65;return(c(e,64,126)||c(e,128,254))&&(o=190*(s-129)+(e-a)),null===(i=null===o?null:g(o,y("gb18030")))&&w(e)&&t.prepend(e),null===i?n(this.fatal):i}return w(e)?e:128===e?8364:c(e,129,254)?(this.gb18030_first=e,null):n(this.fatal)},t}(),A=function(){function t(t,e){void 0===e&&(e=void 0),this.gbk_flag=e,this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;if(58853===e)return i(e);if(this.gbk_flag&&8364===e)return 128;var n=b(e,y("gb18030"));if(null!==n){var r=n%190;return[Math.floor(n/190)+129,r+(r<63?64:65)]}if(this.gbk_flag)return i(e);n=function(t){if(59335===t)return 7457;for(var e=0,n=0,i=y("gb18030-ranges"),r=0;r<i.length;++r){var s=h(i[r]);if(!(s[1]<=t))break;e=s[1],n=s[0]}return n+t-e}(e);var s=Math.floor(n/10/126/10);n-=10*s*126*10;var o=Math.floor(n/10/126);n-=10*o*126;var a=Math.floor(n/10);return[s+129,o+48,a+129,n-10*a+48]},t}();!function(t){t[t.ASCII=0]="ASCII",t[t.Roman=1]="Roman",t[t.Katakana=2]="Katakana",t[t.LeadByte=3]="LeadByte",t[t.TrailByte=4]="TrailByte",t[t.EscapeStart=5]="EscapeStart",t[t.Escape=6]="Escape"}(m||(m={}));var C,T=function(){function t(t){this.fatal=t.fatal,this.iso2022jp_decoder_state=m.ASCII,this.iso2022jp_decoder_output_state=m.ASCII,this.iso2022jp_lead=0,this.iso2022jp_output_flag=!1}return t.prototype.handler=function(t,e){switch(this.iso2022jp_decoder_state){default:case m.ASCII:return 27===e?(this.iso2022jp_decoder_state=m.EscapeStart,null):c(e,0,127)&&14!==e&&15!==e&&27!==e?(this.iso2022jp_output_flag=!1,e):e===v?f:(this.iso2022jp_output_flag=!1,n(this.fatal));case m.Roman:return 27===e?(this.iso2022jp_decoder_state=m.EscapeStart,null):92===e?(this.iso2022jp_output_flag=!1,165):126===e?(this.iso2022jp_output_flag=!1,8254):c(e,0,127)&&14!==e&&15!==e&&27!==e&&92!==e&&126!==e?(this.iso2022jp_output_flag=!1,e):e===v?f:(this.iso2022jp_output_flag=!1,n(this.fatal));case m.Katakana:return 27===e?(this.iso2022jp_decoder_state=m.EscapeStart,null):c(e,33,95)?(this.iso2022jp_output_flag=!1,65344+e):e===v?f:(this.iso2022jp_output_flag=!1,n(this.fatal));case m.LeadByte:return 27===e?(this.iso2022jp_decoder_state=m.EscapeStart,null):c(e,33,126)?(this.iso2022jp_output_flag=!1,this.iso2022jp_lead=e,this.iso2022jp_decoder_state=m.TrailByte,null):e===v?f:(this.iso2022jp_output_flag=!1,n(this.fatal));case m.TrailByte:if(27===e)return this.iso2022jp_decoder_state=m.EscapeStart,n(this.fatal);if(c(e,33,126)){this.iso2022jp_decoder_state=m.LeadByte;var i=g(94*(this.iso2022jp_lead-33)+e-33,y("jis0208"));return null===i?n(this.fatal):i}return e===v?(this.iso2022jp_decoder_state=m.LeadByte,t.prepend(e),n(this.fatal)):(this.iso2022jp_decoder_state=m.LeadByte,n(this.fatal));case m.EscapeStart:return 36===e||40===e?(this.iso2022jp_lead=e,this.iso2022jp_decoder_state=m.Escape,null):(t.prepend(e),this.iso2022jp_output_flag=!1,this.iso2022jp_decoder_state=this.iso2022jp_decoder_output_state,n(this.fatal));case m.Escape:var r=this.iso2022jp_lead;this.iso2022jp_lead=0;var s=null;if(40===r&&66===e&&(s=m.ASCII),40===r&&74===e&&(s=m.Roman),40===r&&73===e&&(s=m.Katakana),36!==r||64!==e&&66!==e||(s=m.LeadByte),null!==s){this.iso2022jp_decoder_state=this.iso2022jp_decoder_state=s;var o=this.iso2022jp_output_flag;return this.iso2022jp_output_flag=!0,o?n(this.fatal):null}return t.prepend([r,e]),this.iso2022jp_output_flag=!1,this.iso2022jp_decoder_state=this.iso2022jp_decoder_output_state,n(this.fatal)}},t}();!function(t){t[t.ASCII=0]="ASCII",t[t.Roman=1]="Roman",t[t.jis0208=2]="jis0208"}(C||(C={}));var U=function(){function t(t){this.fatal=t.fatal,this.iso2022jp_state=C.ASCII}return t.prototype.handler=function(t,e){if(e===v&&this.iso2022jp_state!==C.ASCII)return t.prepend(e),this.iso2022jp_state=C.ASCII,[27,40,66];if(e===v&&this.iso2022jp_state===C.ASCII)return f;if(!(this.iso2022jp_state!==C.ASCII&&this.iso2022jp_state!==C.Roman||14!==e&&15!==e&&27!==e))return i(65533);if(this.iso2022jp_state===C.ASCII&&j(e))return e;if(this.iso2022jp_state===C.Roman&&(j(e)&&92!==e&&126!==e||165==e||8254==e)){if(j(e))return e;if(165===e)return 92;if(8254===e)return 126}if(j(e)&&this.iso2022jp_state!==C.ASCII)return t.prepend(e),this.iso2022jp_state=C.ASCII,[27,40,66];if((165===e||8254===e)&&this.iso2022jp_state!==C.Roman)return t.prepend(e),this.iso2022jp_state=C.Roman,[27,40,74];8722===e&&(e=65293);var n=b(e,y("jis0208"));return null===n?i(e):this.iso2022jp_state!==C.jis0208?(t.prepend(e),this.iso2022jp_state=C.jis0208,[27,36,66]):[Math.floor(n/94)+33,n%94+33]},t}(),L=function(){function t(t){this.fatal=t.fatal,this.Shift_JIS_lead=0}return t.prototype.handler=function(t,e){if(e===v&&0!==this.Shift_JIS_lead)return this.Shift_JIS_lead=0,n(this.fatal);if(e===v&&0===this.Shift_JIS_lead)return f;if(0!==this.Shift_JIS_lead){var i=this.Shift_JIS_lead,r=null;this.Shift_JIS_lead=0;var s=e<127?64:65,o=i<160?129:193;if((c(e,64,126)||c(e,128,252))&&(r=188*(i-o)+e-s),c(r,8836,10715))return 48508+r;var a=null===r?null:g(r,y("jis0208"));return null===a&&w(e)&&t.prepend(e),null===a?n(this.fatal):a}return w(e)||128===e?e:c(e,161,223)?65216+e:c(e,129,159)||c(e,224,252)?(this.Shift_JIS_lead=e,null):n(this.fatal)},t}(),M=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e)||128===e)return e;if(165===e)return 92;if(8254===e)return 126;if(c(e,65377,65439))return e-65377+161;8722===e&&(e=65293);var n=function(t){return(u=u||y("jis0208").map((function(t,e){return c(e,8272,8835)?null:t}))).indexOf(t)}(e);if(null===n)return i(e);var r=Math.floor(n/188),s=n%188;return[r+(r<31?129:193),s+(s<63?64:65)]},t}(),J=function(){function t(t,e){this.index=t,this.fatal=e.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(w(e))return e;var i=this.index[e-128];return i||n(this.fatal)},t}(),R=function(){function t(t,e){this.index=t,this.fatal=e.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;var n=b(e,this.index);return null===n&&i(e),n+128},t}();function K(t,e){var n=t>>8,i=255&t;return e?[n,i]:[i,n]}var F=function(){function t(t,e){this.utf16_be=t,this.fatal=e.fatal,this.utf16_lead_byte=null,this.utf16_lead_surrogate=null}return t.prototype.handler=function(t,e){if(e===v&&(null!==this.utf16_lead_byte||null!==this.utf16_lead_surrogate))return n(this.fatal);if(e===v&&null===this.utf16_lead_byte&&null===this.utf16_lead_surrogate)return f;if(null===this.utf16_lead_byte)return this.utf16_lead_byte=e,null;var i;if(i=this.utf16_be?(this.utf16_lead_byte<<8)+e:(e<<8)+this.utf16_lead_byte,this.utf16_lead_byte=null,null!==this.utf16_lead_surrogate){var r=this.utf16_lead_surrogate;return this.utf16_lead_surrogate=null,c(i,56320,57343)?65536+1024*(r-55296)+(i-56320):(t.prepend(K(i,this.utf16_be)),n(this.fatal))}return c(i,55296,56319)?(this.utf16_lead_surrogate=i,null):c(i,56320,57343)?n(this.fatal):i},t}(),P=function(){function t(t,e){this.utf16_be=t,this.fatal=e.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(c(e,0,65535))return K(e,this.utf16_be);var n=K(55296+(e-65536>>10),this.utf16_be),i=K(56320+(e-65536&1023),this.utf16_be);return n.concat(i)},t}(),D=function(){function t(t){this.fatal=t.fatal,this.utf8_code_point=0,this.utf8_bytes_seen=0,this.utf8_bytes_needed=0,this.utf8_lower_boundary=128,this.utf8_upper_boundary=191}return t.prototype.handler=function(t,e){if(e===v&&0!==this.utf8_bytes_needed)return this.utf8_bytes_needed=0,n(this.fatal);if(e===v)return f;if(0===this.utf8_bytes_needed){if(c(e,0,127))return e;if(c(e,194,223))this.utf8_bytes_needed=1,this.utf8_code_point=31&e;else if(c(e,224,239))224===e&&(this.utf8_lower_boundary=160),237===e&&(this.utf8_upper_boundary=159),this.utf8_bytes_needed=2,this.utf8_code_point=15&e;else{if(!c(e,240,244))return n(this.fatal);240===e&&(this.utf8_lower_boundary=144),244===e&&(this.utf8_upper_boundary=143),this.utf8_bytes_needed=3,this.utf8_code_point=7&e}return null}if(!c(e,this.utf8_lower_boundary,this.utf8_upper_boundary))return this.utf8_code_point=this.utf8_bytes_needed=this.utf8_bytes_seen=0,this.utf8_lower_boundary=128,this.utf8_upper_boundary=191,t.prepend(e),n(this.fatal);if(this.utf8_lower_boundary=128,this.utf8_upper_boundary=191,this.utf8_code_point=this.utf8_code_point<<6|63&e,this.utf8_bytes_seen+=1,this.utf8_bytes_seen!==this.utf8_bytes_needed)return null;var i=this.utf8_code_point;return this.utf8_code_point=this.utf8_bytes_needed=this.utf8_bytes_seen=0,i},t}(),z=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){if(e===v)return f;if(j(e))return e;var n,i;c(e,128,2047)?(n=1,i=192):c(e,2048,65535)?(n=2,i=224):c(e,65536,1114111)&&(n=3,i=240);for(var r=[(e>>6*n)+i];n>0;){var s=e>>6*(n-1);r.push(128|63&s),n-=1}return r},t}(),G=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){return e===v?f:w(e)?e:63360+e-128},t}(),N=function(){function t(t){this.fatal=t.fatal}return t.prototype.handler=function(t,e){return e===v?f:j(e)?e:c(e,63360,63487)?e-63360+128:i(e)},t}(),q=p(),H={"UTF-8":function(t){return new z(t)},GBK:function(t){return new A(t,!0)},gb18030:function(t){return new A(t)},Big5:function(t){return new I(t)},"EUC-JP":function(t){return new x(t)},"ISO-2022-JP":function(t){return new U(t)},Shift_JIS:function(t){return new M(t)},"EUC-KR":function(t){return new B(t)},"UTF-16BE":function(t){return new P(!0,t)},"UTF-16LE":function(t){return new P(!1,t)},"x-user-defined":function(t){return new N(t)}},Q={"UTF-8":function(t){return new D(t)},GBK:function(t){return new O(t)},gb18030:function(t){return new O(t)},Big5:function(t){return new S(t)},"EUC-JP":function(t){return new k(t)},"ISO-2022-JP":function(t){return new T(t)},Shift_JIS:function(t){return new L(t)},"EUC-KR":function(t){return new E(t)},"UTF-16BE":function(t){return new F(!0,t)},"UTF-16LE":function(t){return new F(!1,t)},"x-user-defined":function(t){return new G(t)}},V=!1;function W(){V||(V=!0,s.forEach((function(t){"Legacy single-byte encodings"===t.heading&&t.encodings.forEach((function(t){var e=t.name,n=y(e.toLowerCase());Q[e]=function(t){return new J(n,t)},H[e]=function(t){return new R(n,t)}}))})))}q&&W(),"undefined"!=typeof window&&(window.loadEncodingIndexes=W);var X=function(){function t(t){this.tokens=Array.from(t),this.tokens.reverse()}return t.prototype.endOfStream=function(){return!this.tokens.length},t.prototype.read=function(){return this.tokens.length?this.tokens.pop():v},t.prototype.prepend=function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.push(e.pop());else this.tokens.push(t)},t.prototype.push=function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.unshift(e.shift());else this.tokens.unshift(t)},t}(),Y=function(){function t(t,n){t=void 0!==t?String(t):e;var i=d(n);this._encoding=null,this._decoder=null,this._ignoreBOM=!1,this._BOMseen=!1,this._error_mode="replacement",this._do_not_flush=!1;var s=r(t);if(null===s||"replacement"===s.name)throw RangeError("Unknown encoding: "+t);if(!Q[s.name])throw Error("Decoder not present. Did you forget to include encoding-indexes.js first?");this._encoding=s,Boolean(i.fatal)&&(this._error_mode="fatal"),Boolean(i.ignoreBOM)&&(this._ignoreBOM=!0)}return Object.defineProperty(t.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"fatal",{get:function(){return"fatal"===this._error_mode},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"ignoreBOM",{get:function(){return this._ignoreBOM},enumerable:!0,configurable:!0}),t.prototype.decode=function(t,e){var n=function(t){if("object"!=typeof t)return new Uint8Array(0);if(Z(t))return new Uint8Array(t);if("buffer"in t&&Z(t.buffer))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);return new Uint8Array(0)}(t),i=d(e);this._do_not_flush||(this._decoder=Q[this._encoding.name]({fatal:"fatal"===this._error_mode}),this._BOMseen=!1),this._do_not_flush=Boolean(i.stream);for(var r,s=new X(n),o=[];;){var a=s.read();if(a===v)break;if((r=this._decoder.handler(s,a))===f)break;null!==r&&(Array.isArray(r)?o.push.apply(o,r):o.push(r))}if(!this._do_not_flush){do{if((r=this._decoder.handler(s,s.read()))===f)break;r&&(Array.isArray(r)?o.push.apply(o,r):o.push(r))}while(!s.endOfStream());this._decoder=null}return this.serializeStream(o)},t.prototype.serializeStream=function(t){var e,n;return e=["UTF-8","UTF-16LE","UTF-16BE"],n=this._encoding.name,-1===e.indexOf(n)||this._ignoreBOM||this._BOMseen||(t.length>0&&65279===t[0]?(this._BOMseen=!0,t.shift()):t.length>0&&(this._BOMseen=!0)),function(t){for(var e="",n=0;n<t.length;++n){var i=t[n];i<=65535?e+=String.fromCharCode(i):(i-=65536,e+=String.fromCharCode(55296+(i>>10),56320+(1023&i)))}return e}(t)},t}();function Z(t){try{return t instanceof ArrayBuffer}catch(t){return console.error(t),!1}}var $=function(){function t(t,n){var i=d(n);if(this._encoding=null,this._encoder=null,this._do_not_flush=!1,this._fatal=Boolean(i.fatal)?"fatal":"replacement",Boolean(i.NONSTANDARD_allowLegacyEncoding)){var s=r(t=t?String(t):e);if(null===s||"replacement"===s.name)throw RangeError("Unknown encoding: "+t);if(!H[s.name])throw Error("Encoder not present. Did you forget to include encoding-indexes.js first?");this._encoding=s}else{this._encoding=r("utf-8");var o=_()||{};void 0!==t&&"console"in o&&console.warn("TextEncoder constructor called with encoding label, which is ignored.")}}return Object.defineProperty(t.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()},enumerable:!0,configurable:!0}),t.prototype.encode=function(t,e){t=void 0===t?"":String(t);var n=d(e);this._do_not_flush||(this._encoder=H[this._encoding.name]({fatal:"fatal"===this._fatal})),this._do_not_flush=Boolean(n.stream);for(var i,r=new X(function(t){for(var e=String(t),n=e.length,i=0,r=[];i<n;){var s=e.charCodeAt(i);if(s<55296||s>57343)r.push(s);else if(56320<=s&&s<=57343)r.push(65533);else if(55296<=s&&s<=56319)if(i===n-1)r.push(65533);else{var o=e.charCodeAt(i+1);if(56320<=o&&o<=57343){var a=1023&s,u=1023&o;r.push(65536+(a<<10)+u),i+=1}else r.push(65533)}i+=1}return r}(t)),s=[];;){var o=r.read();if(o===v)break;if((i=this._encoder.handler(r,o))===f)break;Array.isArray(i)?s.push.apply(s,i):s.push(i)}if(!this._do_not_flush){for(;(i=this._encoder.handler(r,r.read()))!==f;)Array.isArray(i)?s.push.apply(s,i):s.push(i);this._encoder=null}return new Uint8Array(s)},t}();if("undefined"!=typeof window){var tt=function(t){return!(t in window)||void 0===window[t]||null===window[t]};tt("TextDecoder")&&(window.TextDecoder=Y),tt("TextEncoder")&&(window.TextEncoder=$)}t.TextDecoder=Y,t.TextEncoder=$,Object.defineProperty(t,"__esModule",{value:!0})}));
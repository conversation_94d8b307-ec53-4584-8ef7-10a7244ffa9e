<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <title>Regexper - Documentation</title>

    <meta name="description" content="Regular expression visualizer using railroad diagrams" />
    <meta name="viewport" content="width=device-width" />
    <meta name="theme-color" content="#bada55" />

    <link rel="shortcut icon" href="favicon.ico" />
    <link rel="author" href="humans.txt" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Bangers&amp;text=Regxpr" />
    <!-- Built: 2020-09-05T18:12:22.844Z -->
  <link href="main-a4262a4d535cb7870494.css" rel="stylesheet"></head>
  <body>
    <header>
      <div class="logo">
        <h1><a href="index.html">Regexper</a></h1>
        <!-- n. One who regexpes -->
        <span>You thought you only had two problems&hellip;</span>
      </div>

      <nav>
        <ul>
          <li>
            <a class="inline-icon" href="changelog.html"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#list-rich" /></svg>Changelog</a>
          </li>
          <li>
            <a class="inline-icon" href="documentation.html"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#document" /></svg>Documentation</a>
          </li>
          <li>
            <a class="inline-icon" href="https://gitlab.com/javallone/regexper-static"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#code" /></svg>Source on GitLab</a>
          </li>
        </ul>
      </nav>
    </header>

    <main id="content">

<div class="copy documentation">
  <section>
    <h1>Reading Railroad Diagrams</h1>

    <p>The images generated by Regexper are commonly referred to as "Railroad Diagrams". These diagram are a straight-forward way to illustrate what can sometimes become very complicated processing in a regular expression, with nested looping and optional elements. The easiest way to read these diagrams to to start at the left and follow the lines to the right. If you encounter a branch, then there is the option of following one of multiple paths (and those paths can loop back to earlier parts of the diagram). In order for a string to successfully match the regular expression in a diagram, you must be able to fulfill each part of the diagram as you move from left to right and proceed through the entire diagram to the end.</p>

    <figure class="shift-right" data-expr="Lions(?: and|,) tigers,? and bears\. Oh my!"></figure>

    <p>As an example, this expression will match "Lions and tigers and bears. Oh my!" or the more grammatically correct "Lions, tigers, and bears. Oh my!" (with or without an Oxford comma). The diagram first matches the string "Lions"; you cannot proceed without that in your input. Then there is a choice between a comma or the string " and". No matter what choice you make, the input string must then contain " tigers" followed by an optional comma (your path can either go through the comma or around it). Finally the string must end with " and bears. Oh my!".</p>

    <section>
      <h2>Basic parts of these diagrams</h2>

      <p>The simplest pieces of these diagrams to understand are the parts that match some specific bit of text without an options. They are: Literals, Escape sequences, and "Any charater".</p>

      <div class="section">
        <h3>Literals</h3>

        <figure class="shift-left" data-expr="A literal example"></figure>

        <p>Literals match an exact string of text. They're displayed in a light blue box, and the contents are quoted (to make it easier to see any leading or trailing whitespace).</p>
      </div>

      <div class="section">
        <h3>Escape sequences</h3>

        <figure class="shift-left" data-expr="\w\x7f\u00bb\1\0"></figure>

        <p>Escape sequences are displayed in a green box and contain a description of the type of character(s) they will match.</p>
      </div>

      <div class="section">
        <h3>"Any character"</h3>

        <figure class="shift-left" data-expr="."></figure>

        <p>"Any character" is similar to an escape sequence. It matches any single character.</p>
      </div>
    </section>

    <section>
      <h2>Character Sets</h2>

      <figure class="shift-left" data-expr="[#a-z\n][^$0-9\b]"></figure>

      <p>Character sets will match or not match a collection of individual characters. They are shown as a box containing literals and escape sequences. The label at the top indicates that the character set will match "One of" the contained items or "None of" the contained items.</p>
    </section>

    <section>
      <h2>Subexpressions</h2>

      <figure class="shift-left" data-expr="(example\s)(?=content)"></figure>

      <p>Subexpressions are indicated by a dotted outline around the items that are in the expression. Captured subexpressions are labeled with the group number they will be captured under. Positive and negative lookahead are labeled as such.</p>
    </section>

    <section>
      <h2>Alternation</h2>

      <figure class="shift-left" data-expr="one\s|two\W|three\t|four\n"></figure>

      <p>Alternation provides choices for the regular experssion. It is indicated by the path for the expression fanning out into a number of choices.</p>
    </section>

    <section>
      <h2>Quantifiers</h2>

      <p>Quantifiers indicate if part of the expression should be repeated or optional. They are displayed similarly to Alternation, by the path through the diagram branching (and possibly looping back on itself). Unless indicated by an arrow on the path, the preferred path is to continue going straight.</p>

      <div class="section">
        <h3>Zero-or-more</h3>

        <figure class="shift-left" data-expr="(?:greedy)*">
          <figcaption>Greedy quantifier</figcaption>
        </figure>

        <figure class="shift-left" data-expr="(?:non-greedy)*?">
          <figcaption>Non-greedy quantifier</figcaption>
        </figure>

        <p>The zero-or-more quantifier matches any number of repetitions of the pattern.</p>
      </div>

      <div class="section">
        <h3>Required</h3>

        <figure class="shift-left" data-expr="(?:greedy)+">
          <figcaption>Greedy quantifier</figcaption>
        </figure>

        <figure class="shift-left" data-expr="(?:non-greedy)+?">
          <figcaption>Non-greedy quantifier</figcaption>
        </figure>

        <p>The required quantifier matches one or more repetitions of the pattern. Note that it does not have the path that allows the pattern to be skipped like the zero-or-more quantifier.</p>
      </div>

      <div class="section">
        <h3>Optional</h3>

        <figure class="shift-left" data-expr="(?:greedy)?">
          <figcaption>Greedy quantifier</figcaption>
        </figure>

        <figure class="shift-left" data-expr="(?:non-greedy)??">
          <figcaption>Non-greedy quantifier</figcaption>
        </figure>

        <p>The optional quantifier matches the pattern at most once. Note that it does not have the path that allows the pattern to loop back on itself like the zero-or-more or required quantifiers.</p>
      </div>

      <div class="section">
        <h3>Range</h3>

        <figure class="shift-left" data-expr="(?:greedy){5,10}">
          <figcaption>Greedy quantifier</figcaption>
        </figure>

        <figure class="shift-left" data-expr="(?:non-greedy){5,10}?">
          <figcaption>Non-greedy quantifier</figcaption>
        </figure>

        <p>The ranged quantifier specifies a number of times the pattern may be repeated. The two examples provided here both have a range of "{5,10}", the label for the looping branch indicates the number of times that branch may be followed. The values are one less than specified in the expression since the pattern would have to be matched once before repeating it is an option. So, for these examples, the pattern would be matched once and then the loop would be followed 4 to 9 times, for a total of 5 to 10 matches of the pattern.</p>
      </div>
    </section>
  </section>
</div>

    </main>

    <footer>
      <ul class="inline-list">
        <li>Created by <a href="mailto:<EMAIL>">Jeff Avallone</a></li>
        <li>
          Generated images licensed:
          <a rel="license" href="http://creativecommons.org/licenses/by/3.0/"><img alt="Creative Commons License" src="https://licensebuttons.net/l/by/3.0/80x15.png" /></a>
        </li>
      </ul>

      <script type="text/html" id="svg-container-base">
        <div class="svg">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:cc="http://creativecommons.org/ns#"
            xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
            version="1.1">
            <defs>
              <style type="text/css">svg {
          background-color: #fff; }
        
        .root text,
        .root tspan {
          font: 12px Arial; }
        
        .root path {
          fill-opacity: 0;
          stroke-width: 2px;
          stroke: #000; }
        
        .root circle {
          fill: #6b6659;
          stroke-width: 2px;
          stroke: #000; }
        
        .anchor text, .any-character text {
          fill: #fff; }
        
        .anchor rect, .any-character rect {
          fill: #6b6659; }
        
        .escape text, .charset-escape text, .literal text {
          fill: #000; }
        
        .escape rect, .charset-escape rect {
          fill: #bada55; }
        
        .literal rect {
          fill: #dae9e5; }
        
        .charset .charset-box {
          fill: #cbcbba; }
        
        .subexp .subexp-label tspan,
        .charset .charset-label tspan,
        .match-fragment .repeat-label tspan {
          font-size: 10px; }
        
        .repeat-label {
          cursor: help; }
        
        .subexp .subexp-label tspan,
        .charset .charset-label tspan {
          dominant-baseline: text-after-edge; }
        
        .subexp .subexp-box {
          stroke: #908c83;
          stroke-dasharray: 6,2;
          stroke-width: 2px;
          fill-opacity: 0; }
        
        .quote {
          fill: #908c83; }
        </style>
            </defs>
            <metadata>
              <rdf:RDF>
                <cc:License rdf:about="http://creativecommons.org/licenses/by/3.0/">
                  <cc:permits rdf:resource="http://creativecommons.org/ns#Reproduction" />
                  <cc:permits rdf:resource="http://creativecommons.org/ns#Distribution" />
                  <cc:requires rdf:resource="http://creativecommons.org/ns#Notice" />
                  <cc:requires rdf:resource="http://creativecommons.org/ns#Attribution" />
                  <cc:permits rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
                </cc:License>
              </rdf:RDF>
            </metadata>
          </svg>
        </div>
        <div class="progress">
          <div style="width:0;"></div>
        </div>
      </script>
    </footer>

    <svg xmlns="http://www.w3.org/2000/svg" id="open-iconic">
      <!-- These icon are from the Open Iconic project https://useiconic.com/open/ -->
      <defs>
        <g id="code">
          <path d="M5 0l-3 6h1l3-6h-1zm-4 1l-1 2 1 2h1l-1-2 1-2h-1zm5 0l1 2-1 2h1l1-2-1-2h-1z" transform="translate(0 1)" />
        </g>
        <g id="data-transfer-download">
          <path d="M3 0v3h-2l3 3 3-3h-2v-3h-2zm-3 7v1h8v-1h-8z" />
        </g>
        <g id="document">
          <path d="M0 0v8h7v-4h-4v-4h-3zm4 0v3h3l-3-3zm-3 2h1v1h-1v-1zm0 2h1v1h-1v-1zm0 2h4v1h-4v-1z" />
        </g>
        <g id="link-intact">
          <path d="M5.88.03c-.18.01-.36.03-.53.09-.27.1-.53.25-.75.47a.5.5 0 1 0 .69.69c.11-.11.24-.17.38-.22.35-.12.78-.07 **********.39.39 1.04 0 1.44l-1.5 1.5c-.44.44-.8.48-1.06.47-.26-.01-.41-.13-.41-.13a.5.5 0 1 0-.5.88s.34.22.84.25c.5.03 1.2-.16 1.81-.78l1.5-1.5c.78-.78.78-2.04 0-2.81-.28-.28-.61-.45-.97-.53-.18-.04-.38-.04-.56-.03zm-2 2.31c-.5-.02-1.19.15-1.78.75l-1.5 1.5c-.78.78-.78 2.04 0 2.81.56.56 1.36.72 2.06.47.27-.1.53-.25.75-.47a.5.5 0 1 0-.69-.69c-.11.11-.24.17-.38.22-.35.12-.78.07-1.06-.22-.39-.39-.39-1.04 0-1.44l1.5-1.5c.4-.4.75-.45 1.03-.44.28.01.47.09.47.09a.5.5 0 1 0 .44-.88s-.34-.2-.84-.22z" />
        </g>
        <g id="list-rich">
          <path d="M0 0v3h3v-3h-3zm4 0v1h4v-1h-4zm0 2v1h3v-1h-3zm-4 2v3h3v-3h-3zm4 0v1h4v-1h-4zm0 2v1h3v-1h-3z" />
        </g>
        <g id="warning">
          <path d="M3.09 0c-.06 0-.1.04-.13.09l-2.94 6.81c-.02.05-.03.13-.03.19v.81c0 .***********.09h6.81c.05 0 .09-.04.09-.09v-.81c0-.05-.01-.14-.03-.19l-2.94-6.81c-.02-.05-.07-.09-.13-.09h-.81zm-.09 3h1v2h-1v-2zm0 3h1v1h-1v-1z" />
        </g>
      </defs>
    </svg>
  <script src="main-a4262a4d535cb7870494.js"></script></body>
</html>

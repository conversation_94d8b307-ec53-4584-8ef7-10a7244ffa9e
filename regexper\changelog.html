<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <title>Regexper - Changelog</title>

    <meta name="description" content="Regular expression visualizer using railroad diagrams" />
    <meta name="viewport" content="width=device-width" />
    <meta name="theme-color" content="#bada55" />

    <link rel="shortcut icon" href="favicon.ico" />
    <link rel="author" href="humans.txt" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Bangers&amp;text=Regxpr" />
    <!-- Built: 2020-09-05T18:12:22.844Z -->
  <link href="main-a4262a4d535cb7870494.css" rel="stylesheet"></head>
  <body>
    <header>
      <div class="logo">
        <h1><a href="index.html">Regexper</a></h1>
        <!-- n. One who regexpes -->
        <span>You thought you only had two problems&hellip;</span>
      </div>

      <nav>
        <ul>
          <li>
            <a class="inline-icon" href="changelog.html"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#list-rich" /></svg>Changelog</a>
          </li>
          <li>
            <a class="inline-icon" href="documentation.html"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#document" /></svg>Documentation</a>
          </li>
          <li>
            <a class="inline-icon" href="https://gitlab.com/javallone/regexper-static"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 8 8"><use xlink:href="#code" /></svg>Source on GitLab</a>
          </li>
        </ul>
      </nav>
    </header>

    <main id="content">

<div class="copy changelog">
  <dl>
      <dt>September 5, 2020 Release</dt>
        <dd>Updating build system to use only webpack.</dd>
        <dd>Upgraded most dependencies</dd>
      <dt>June 4, 2018 Release</dt>
        <dd>Moving source to GitLab and updating some links on the site.</dd>
      <dt>May 24, 2018 Release</dt>
        <dd>Supporting browser "Do Not Track" setting. When enabled, this will prevent use of Google Analytics and Sentry error reporting.</dd>
      <dt>February 10, 2018 Release</dt>
        <dd>Adding 'sticky' and 'unicode' flag support</dd>
        <dd>Encoding parenthesis in the permalink and browser URLs</dd>
        <dd>Adding PNG download support</dd>
      <dt>July 31, 2016 Release</dt>
        <dd>Merged code to enable automated testing with Travis CI from <a href="https://github.com/Byron">Sebastian Thiel</a></dd>
        <dd>Merged feature to show an informational tooltip on loop labels from <a href="https://github.com/ThibWeb">Thibaud Colas</a></dd>
        <dd>Fixed issue with '^' and '$' not being allowed in the middle of a fragment (see <a href="https://github.com/javallone/regexper-static/issues/29">GitHub issue</a>)</dd>
        <dd>Updating several dependencies</dd>
        <dd>Some stylistic code cleanup</dd>
      <dt>May 31, 2016 Release</dt>
        <dd>Putting separate CSS for generated SVG images back into the build. Downloaded images have been broken since the March 10 release because the SVG styles were merged into the page styles.</dd>
      <dt>May 23, 2016 Release</dt>
        <dd>Refactored tracking code to support latest Google Analytics setup</dd>
      <dt>March 10, 2016 Release</dt>
        <dd>Embedding SVG icon images into markup</dd>
        <dd>Some changes for minor performance improvements</dd>
        <dd>Updating several dependencies</dd>
      <dt>March 8, 2016 Release</dt>
        <dd>Replaced icon font with individual SVG images</dd>
      <dt>March 3, 2016 Release</dt>
        <dd>Merged some code cleanup and a bugfix from <a href="https://github.com/Byron">Sebastian Thiel</a></dd>
        <dd>Updated notice for IE8 users to no longer include link to legacy site</dd>
      <dt>December 21, 2015 Release</dt>
        <dd>Updating NPM dependencies to fix JS error that only appeared when running site from a local development environment (see <a href="https://github.com/javallone/regexper-static/issues/21">GitHub issue</a>)</dd>
      <dt>November 10, 2015 Release</dt>
        <dd>Fixing Babel integration to include polyfills</dd>
      <dt>November 1, 2015 Release</dt>
        <dd>Switching from Compass to node-sass and Bourbon (no more need for Ruby)</dd>
        <dd>Switching to Babel instead of es6ify</dd>
        <dd>Improving sourcemap generation</dd>
        <dd>Cleanup of the build process</dd>
      <dt>October 31, 2015 Release</dt>
        <dd>Reducing file size for title font</dd>
        <dd>Cleaning up gulpfile</dd>
        <dd>Upgrading most dependencies</dd>
        <dd>Switching to Handlebars for template rendering</dd>
      <dt>September 17, 2015 Release</dt>
        <dd>Fixing styling of labels on repetitions</dd>
        <dd>Fixing issue with vertical centering of alternation expressions that include empty expressions (see <a href="https://github.com/javallone/regexper-static/pull/16">GitHub issue</a>)</dd>
      <dt>September 2, 2015 Release</dt>
        <dd>Merging fix for error reporting from (see <a href="https://github.com/javallone/regexper-static/pull/15">GitHub pull request</a>)</dd>
      <dt>July 5, 2015 Release</dt>
        <dd>Updating Creative Commons license badge URL so it isn't pointing to a redirecting URL anymore</dd>
      <dt>June 22, 2015 Release</dt>
        <dd>Tweaking buggy Firefox hash detection code based on JavaScript errors that were logged</dd>
      <dt>June 16, 2015 Release</dt>
        <dd>Fixes issue with expressions containing a "%" not rendering in Firefox (see <a href="https://github.com/javallone/regexper-static/issues/12">GitHub issue</a>)</dd>
        <dd>Fixed rendering in IE that was causing "--&gt;" to display at the top of the page.</dd>
      <dt>April 14, 2015 Release</dt>
        <dd>Rendering speed improved. Most users will probably not see much improvement since logging data indicates that expressing rendering time is typically less than 1 second. Using the <a href="http://www.ex-parrot.com/pdw/Mail-RFC822-Address.html">RFC822 email regular expression</a> though shows a rendering speed improvement from ~120 seconds down to ~80 seconds.</dd>
        <dd>Fixing a bug that would only occur when attempting to render an expression while another is in the process of rendering</dd>
      <dt>March 14, 2015 Release</dt>
        <dd>Removing use of Q for promises in favor of "native" ES6 promises (even though they aren't quite native everywhere yet)</dd>
      <dt>March 13, 2015 Release</dt>
        <dd>Fixes bug with numbering of nested subexpressions (see <a href="https://github.com/javallone/regexper-static/issues/7">GitHub issue</a>)</dd>
      <dt>February 11, 2015 Release</dt>
        <dd>Various adjustments to analytics: tracking expression rendering time and JS errors</dd>
        <dd>Escape sequences that match to a specific character now display their hexadecimal code (actually done on January 25, but I forgot to update the changelog)</dd>
        <dd>Fixing styling issue with header links (see <a href="https://github.com/javallone/regexper-static/issues/5">GitHub issue</a>)</dd>
      <dt>December 30, 2014 Release</dt>
        <dd>Fixing bug that prevented rendering empty subexpressions</dd>
        <dd>Fixing minor styling bug when permalink is disabled</dd>
        <dd>Cleaning up some duplicated styles and JS</dd>
      <dt>December 29, 2014 Release</dt>
        <dd>Tweaking analytics data to help with addressing issues in deployed code (work will likely continue on this)</dd>
        <dd>Added progress bars on the documentation page</dd>
        <dd>Removed the loading spinner everywhere</dd>
        <dd>Animated the progress bars</dd>
      <dt>December 26, 2014 Release</dt>
        <dd>Freshened up design</dd>
        <dd>Multiline regular expression input field (press Shift-Enter to render)</dd>
        <dd>Added a changelog</dd>
        <dd>Added documentation</dd>
        <dd>All parsing and rendering happens client-side (using <a href="http://canopy.jcoglan.com/">Canopy</a> and <a href="http://snapsvg.io/">Snap.svg</a>)</dd>
        <dd>Added Download link (not available in older browsers)</dd>
        <dd>Added display of regular expression flags (ignore case, global, multiline)</dd>
        <dd>Added indicator of quantifier greedy-ness</dd>
        <dd>Various improvements to parsing of regular expression</dd>
        <dd>Rendering of a regular expression can be canceled by pressing Escape</dd>
  </dl>
</div>

    </main>

    <footer>
      <ul class="inline-list">
        <li>Created by <a href="mailto:<EMAIL>">Jeff Avallone</a></li>
        <li>
          Generated images licensed:
          <a rel="license" href="http://creativecommons.org/licenses/by/3.0/"><img alt="Creative Commons License" src="https://licensebuttons.net/l/by/3.0/80x15.png" /></a>
        </li>
      </ul>

      <script type="text/html" id="svg-container-base">
        <div class="svg">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:cc="http://creativecommons.org/ns#"
            xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
            version="1.1">
            <defs>
              <style type="text/css">svg {
          background-color: #fff; }
        
        .root text,
        .root tspan {
          font: 12px Arial; }
        
        .root path {
          fill-opacity: 0;
          stroke-width: 2px;
          stroke: #000; }
        
        .root circle {
          fill: #6b6659;
          stroke-width: 2px;
          stroke: #000; }
        
        .anchor text, .any-character text {
          fill: #fff; }
        
        .anchor rect, .any-character rect {
          fill: #6b6659; }
        
        .escape text, .charset-escape text, .literal text {
          fill: #000; }
        
        .escape rect, .charset-escape rect {
          fill: #bada55; }
        
        .literal rect {
          fill: #dae9e5; }
        
        .charset .charset-box {
          fill: #cbcbba; }
        
        .subexp .subexp-label tspan,
        .charset .charset-label tspan,
        .match-fragment .repeat-label tspan {
          font-size: 10px; }
        
        .repeat-label {
          cursor: help; }
        
        .subexp .subexp-label tspan,
        .charset .charset-label tspan {
          dominant-baseline: text-after-edge; }
        
        .subexp .subexp-box {
          stroke: #908c83;
          stroke-dasharray: 6,2;
          stroke-width: 2px;
          fill-opacity: 0; }
        
        .quote {
          fill: #908c83; }
        </style>
            </defs>
            <metadata>
              <rdf:RDF>
                <cc:License rdf:about="http://creativecommons.org/licenses/by/3.0/">
                  <cc:permits rdf:resource="http://creativecommons.org/ns#Reproduction" />
                  <cc:permits rdf:resource="http://creativecommons.org/ns#Distribution" />
                  <cc:requires rdf:resource="http://creativecommons.org/ns#Notice" />
                  <cc:requires rdf:resource="http://creativecommons.org/ns#Attribution" />
                  <cc:permits rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
                </cc:License>
              </rdf:RDF>
            </metadata>
          </svg>
        </div>
        <div class="progress">
          <div style="width:0;"></div>
        </div>
      </script>
    </footer>

    <svg xmlns="http://www.w3.org/2000/svg" id="open-iconic">
      <!-- These icon are from the Open Iconic project https://useiconic.com/open/ -->
      <defs>
        <g id="code">
          <path d="M5 0l-3 6h1l3-6h-1zm-4 1l-1 2 1 2h1l-1-2 1-2h-1zm5 0l1 2-1 2h1l1-2-1-2h-1z" transform="translate(0 1)" />
        </g>
        <g id="data-transfer-download">
          <path d="M3 0v3h-2l3 3 3-3h-2v-3h-2zm-3 7v1h8v-1h-8z" />
        </g>
        <g id="document">
          <path d="M0 0v8h7v-4h-4v-4h-3zm4 0v3h3l-3-3zm-3 2h1v1h-1v-1zm0 2h1v1h-1v-1zm0 2h4v1h-4v-1z" />
        </g>
        <g id="link-intact">
          <path d="M5.88.03c-.18.01-.36.03-.53.09-.27.1-.53.25-.75.47a.5.5 0 1 0 .69.69c.11-.11.24-.17.38-.22.35-.12.78-.07 **********.39.39 1.04 0 1.44l-1.5 1.5c-.44.44-.8.48-1.06.47-.26-.01-.41-.13-.41-.13a.5.5 0 1 0-.5.88s.34.22.84.25c.5.03 1.2-.16 1.81-.78l1.5-1.5c.78-.78.78-2.04 0-2.81-.28-.28-.61-.45-.97-.53-.18-.04-.38-.04-.56-.03zm-2 2.31c-.5-.02-1.19.15-1.78.75l-1.5 1.5c-.78.78-.78 2.04 0 2.81.56.56 1.36.72 2.06.47.27-.1.53-.25.75-.47a.5.5 0 1 0-.69-.69c-.11.11-.24.17-.38.22-.35.12-.78.07-1.06-.22-.39-.39-.39-1.04 0-1.44l1.5-1.5c.4-.4.75-.45 1.03-.44.28.01.47.09.47.09a.5.5 0 1 0 .44-.88s-.34-.2-.84-.22z" />
        </g>
        <g id="list-rich">
          <path d="M0 0v3h3v-3h-3zm4 0v1h4v-1h-4zm0 2v1h3v-1h-3zm-4 2v3h3v-3h-3zm4 0v1h4v-1h-4zm0 2v1h3v-1h-3z" />
        </g>
        <g id="warning">
          <path d="M3.09 0c-.06 0-.1.04-.13.09l-2.94 6.81c-.02.05-.03.13-.03.19v.81c0 .***********.09h6.81c.05 0 .09-.04.09-.09v-.81c0-.05-.01-.14-.03-.19l-2.94-6.81c-.02-.05-.07-.09-.13-.09h-.81zm-.09 3h1v2h-1v-2zm0 3h1v1h-1v-1z" />
        </g>
      </defs>
    </svg>
  <script src="main-a4262a4d535cb7870494.js"></script></body>
</html>
